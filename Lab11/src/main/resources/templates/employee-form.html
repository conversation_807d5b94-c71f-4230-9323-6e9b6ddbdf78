<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle != null ? pageTitle : 'Employee Form'}">Employee Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header Styles */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }

        /* Main content area */
        .main-content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 30px 20px;
        }

        /* Footer Styles */
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: auto;
        }
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 0 20px;
        }
        .page-title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #333;
            margin-bottom: 20px;
        }
        .form-container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="email"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="email"]:focus, select:focus {
            border-color: #007bff;
            outline: none;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #17a2b8;
            color: white;
        }
        .btn-primary:hover {
            background-color: #138496;
        }
        .back-link {
            color: #007bff;
            text-decoration: none;
            margin-top: 20px;
            display: inline-block;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .field-error {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <h1>Employee Management System</h1>
            </div>
            <div>
                <a th:href="@{/employees}" style="color: white; text-decoration: none;">← Back to List</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="form-container">
            <h1 class="page-title" th:text="${pageTitle != null ? pageTitle : 'Employee Form'}">Create a new Employee.</h1>

            <div th:if="${successMessage}" class="message success" th:text="${successMessage}"></div>
            <div th:if="${errorMessage}" class="message error" th:text="${errorMessage}"></div>
        <h2>Employee Management System</h2>
        <h3 th:text="${employee.id != null ? 'Update Employee' : 'Save Employee'}">Save Employee</h3>

        <!-- Display general error message -->
        <div th:if="${errorMessage}" class="message error" th:text="${errorMessage}"></div>

        <form th:action="@{/employees/save}" th:object="${employee}" method="post">
            <input type="hidden" th:field="*{id}">

            <div class="form-group">
                <input type="text" th:field="*{firstName}" placeholder="First Name (5-20 characters)" required
                       th:class="${#fields.hasErrors('firstName')} ? 'error-input' : ''">
                <div th:if="${#fields.hasErrors('firstName')}" class="field-error" th:errors="*{firstName}"></div>
            </div>

            <div class="form-group">
                <input type="text" th:field="*{lastName}" placeholder="Last Name" required
                       th:class="${#fields.hasErrors('lastName')} ? 'error-input' : ''">
                <div th:if="${#fields.hasErrors('lastName')}" class="field-error" th:errors="*{lastName}"></div>
            </div>

            <div class="form-group">
                <input type="email" th:field="*{email}" placeholder="Email" required
                       th:class="${#fields.hasErrors('email')} ? 'error-input' : ''">
                <div th:if="${#fields.hasErrors('email')}" class="field-error" th:errors="*{email}"></div>
            </div>

            <button type="submit" class="btn btn-primary"
                    th:text="${employee.id != null ? 'Update Employee' : 'Save Employee'}">Save Employee</button>
        </form>

        <a th:href="@{/employees}" class="back-link">Back to Employee List</a>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div>
                <p>2025 Employee Management System. All rights reserved.</p>
            </div>
        </div>
    </footer>

</body>
</html>