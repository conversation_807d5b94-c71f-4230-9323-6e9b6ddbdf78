<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employees List</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header Styles */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }

        /* Main content area */
        .main-content {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
            width: 100%;
            box-sizing: border-box;
        }

        /* Footer Styles */
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: auto;
        }
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 0 20px;
        }
        .footer-logo {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            color: white;
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .page-title {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logout-btn {
            background-color: #dc3545;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .logout-btn:hover {
            background-color: #c82333;
            text-decoration: none;
            color: white;
        }
        .add-btn {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            display: inline-block;
        }
        .add-btn:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #007bff;
        }
        .action-btn {
            padding: 6px 12px;
            margin: 2px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .update-btn {
            background-color: #007bff;
            color: white;
        }
        .update-btn:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
        }
        .delete-btn:hover {
            background-color: #c82333;
            color: white;
            text-decoration: none;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <h1>Employee Management System</h1>
            </div>
            <div class="user-info">
                <span th:text="'Welcome, ' + ${loggedInName} + ' (' + ${loggedInRole} + ')'">Welcome</span>
                <a th:href="@{/logout}" class="logout-btn">Logout</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <h2 class="page-title">Employees List</h2>
        </div>

    <div th:if="${successMessage}" class="message success" th:text="${successMessage}"></div>
    <div th:if="${errorMessage}" class="message error" th:text="${errorMessage}"></div>

    <a th:if="${isAdmin}" th:href="@{/employees/new}" class="add-btn">Add Employee</a>

    <table th:if="${not #lists.isEmpty(employees)}">
        <thead>
            <tr>
                <th>Employee First Name</th>
                <th>Employee Last Name</th>
                <th>Employee Email</th>
                <th th:if="${isAdmin}">Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr th:each="employee : ${employees}">
                <td th:text="${employee.firstName}">John</td>
                <td th:text="${employee.lastName}">Cena</td>
                <td th:text="${employee.email}"><EMAIL></td>
                <td th:if="${isAdmin}">
                    <a th:href="@{/employees/edit/{id}(id=${employee.id})}" class="action-btn update-btn">Update</a>
                    <a th:href="@{/employees/delete/{id}(id=${employee.id})}"
                       class="action-btn delete-btn"
                       onclick="return confirm('Are you sure you want to delete this employee?')">Delete</a>
                </td>
            </tr>
        </tbody>
    </table>

    <div th:if="${#lists.isEmpty(employees)}">
        <p>No employees found. <a th:if="${isAdmin}" th:href="@{/employees/new}">Add the first employee</a></p>
    </div>

    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div>
                <p>2025 Employee Management System. All rights reserved.</p>
            </div>
        </div>
    </footer>

</body>
</html>