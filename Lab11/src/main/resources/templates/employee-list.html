<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employees List</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        h1 {
            color: #333;
            margin: 0;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logout-btn {
            background-color: #dc3545;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .logout-btn:hover {
            background-color: #c82333;
            text-decoration: none;
            color: white;
        }
        .add-btn {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            display: inline-block;
        }
        .add-btn:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #007bff;
        }
        .action-btn {
            padding: 6px 12px;
            margin: 2px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .update-btn {
            background-color: #007bff;
            color: white;
        }
        .update-btn:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
        }
        .delete-btn:hover {
            background-color: #c82333;
            color: white;
            text-decoration: none;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Employees List</h1>
        <div class="user-info">
            <span th:text="'Welcome, ' + ${loggedInName}">Welcome</span>
            <a th:href="@{/logout}" class="logout-btn">Logout</a>
        </div>
    </div>

    <div th:if="${successMessage}" class="message success" th:text="${successMessage}"></div>
    <div th:if="${errorMessage}" class="message error" th:text="${errorMessage}"></div>

    <a th:if="${isAdmin}" th:href="@{/employees/new}" class="add-btn">Add Employee</a>

    <table th:if="${not #lists.isEmpty(employees)}">
        <thead>
            <tr>
                <th>Employee First Name</th>
                <th>Employee Last Name</th>
                <th>Employee Email</th>
                <th th:if="${isAdmin}">Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr th:each="employee : ${employees}">
                <td th:text="${employee.firstName}">John</td>
                <td th:text="${employee.lastName}">Cena</td>
                <td th:text="${employee.email}"><EMAIL></td>
                <td th:if="${isAdmin}">
                    <a th:href="@{/employees/edit/{id}(id=${employee.id})}" class="action-btn update-btn">Update</a>
                    <a th:href="@{/employees/delete/{id}(id=${employee.id})}"
                       class="action-btn delete-btn"
                       onclick="return confirm('Are you sure you want to delete this employee?')">Delete</a>
                </td>
            </tr>
        </tbody>
    </table>

    <div th:if="${#lists.isEmpty(employees)}">
        <p>No employees found.</p>
    </div>
</body>
</html>