spring.application.name=lab07

server.port=9090

spring.datasource.url=************************************
spring.datasource.username=root
spring.datasource.password=password

# Hibernate Properties (JPA Implementation)
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update  
spring.jpa.show-sql=true

# Basic configuration - no pre-populated users

# Set logging level for application packages
logging.level.root=INFO
logging.level.com.example=DEBUG
logging.level.org.springframework=WARN
logging.level.org.springframework.boot.autoconfigure.security=WARN
