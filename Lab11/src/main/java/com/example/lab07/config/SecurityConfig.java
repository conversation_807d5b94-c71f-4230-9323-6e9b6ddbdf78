package com.example.lab07.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable()) // Disable CSRF for simplicity
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/login", "/logout", "/api/users/login").permitAll() // Allow login/logout pages
                .anyRequest().permitAll() // Allow all other requests for now (we'll handle auth in controllers)
            )
            .formLogin(form -> form.disable()) // Disable Spring Security's default form login
            .httpBasic(basic -> basic.disable()); // Disable HTTP Basic auth

        return http.build();
    }
}