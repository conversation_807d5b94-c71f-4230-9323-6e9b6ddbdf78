package com.example.lab07.service;

import com.example.lab07.model.Employee;
import com.example.lab07.repository.EmployeeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

@Service
@Validated
public class EmployeeService {

    private final EmployeeRepository employeeRepository;

    @Autowired
    public EmployeeService(EmployeeRepository employeeRepository) {
        this.employeeRepository = employeeRepository;
    }

    public List<Employee> getAllEmployees() {
        return employeeRepository.findAll();
    }

    public Optional<Employee> getEmployeeById(Long id) {
        return employeeRepository.findById(id);
    }

    public Employee saveEmployee(@Valid Employee employee) {
        // Check for duplicate email
        checkDuplicateEmail(employee);
        return employeeRepository.save(employee);
    }

    public boolean deleteEmployee(Long id) {
        if (employeeRepository.existsById(id)) {
            employeeRepository.deleteById(id);
            return true;
        }
        return false;
    }

    private void checkDuplicateEmail(Employee employee) {
        if (employee.getId() == null) {
            Optional<Employee> existingEmployee = employeeRepository.findByEmailIgnoreCase(employee.getEmail());
            if (existingEmployee.isPresent()) {
                throw new IllegalArgumentException("Email already exists");
            }
        } else {
            Optional<Employee> existingEmployee = employeeRepository.findByEmailIgnoreCaseAndIdNot(employee.getEmail(), employee.getId());
            if (existingEmployee.isPresent()) {
                throw new IllegalArgumentException("Email already exists");
            }
        }
    }



}
