package com.example.lab07.controller;

import com.example.lab07.model.Employee;
import com.example.lab07.service.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.servlet.http.HttpSession;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/employees")
public class EmployeeController {

    private final EmployeeService employeeService;

    @Autowired
    public EmployeeController(EmployeeService employeeService) {
        this.employeeService = employeeService;
    }

    private boolean isAuthenticated(HttpSession session) {
        return session.getAttribute("loggedInUser") != null;
    }

    // Helper method to check if user has admin role
    private boolean isAdmin(HttpSession session) {
        String role = (String) session.getAttribute("role");
        return "ADMIN".equals(role);
    }

    // Helper method to check if user has admin role, redirect if not
    private String checkAdminAccess(HttpSession session) {
        if (!isAuthenticated(session)) {
            return "redirect:/login";
        }
        if (!isAdmin(session)) {
            return "redirect:/employees?error=access_denied";
        }
        return null;
    }

    @GetMapping
    public String listEmployees(Model model, HttpSession session,
                               @RequestParam(value = "error", required = false) String error) {
        if (!isAuthenticated(session)) {
            return "redirect:/login";
        }

        // Handle access denied error
        if ("access_denied".equals(error)) {
            model.addAttribute("errorMessage", "Access denied. Admin privileges required for this action.");
        }

        List<Employee> employees = employeeService.getAllEmployees();
        model.addAttribute("employees", employees);
        model.addAttribute("totalCount", employees.size());

        model.addAttribute("loggedInUsername", session.getAttribute("username"));
        model.addAttribute("loggedInName", session.getAttribute("name"));
        model.addAttribute("loggedInRole", session.getAttribute("role"));
        model.addAttribute("isAdmin", isAdmin(session));

        return "employee-list";
    }

    @GetMapping("/new")
    public String showNewEmployeeForm(Model model, HttpSession session) {
        // Check admin access
        String accessCheck = checkAdminAccess(session);
        if (accessCheck != null) {
            return accessCheck;
        }

        model.addAttribute("employee", new Employee());
        model.addAttribute("pageTitle", "Add New Employee");
        return "employee-form";
    }

    @GetMapping("/edit/{id}")
    public String showEditEmployeeForm(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes, HttpSession session) {
        // Check admin access
        String accessCheck = checkAdminAccess(session);
        if (accessCheck != null) {
            return accessCheck;
        }

        Optional<Employee> employee = employeeService.getEmployeeById(id);
        if (employee.isPresent()) {
            model.addAttribute("employee", employee.get());
            model.addAttribute("pageTitle", "Edit Employee");
            return "employee-form";
        } else {
            redirectAttributes.addFlashAttribute("errorMessage", "Employee not found with ID: " + id);
            return "redirect:/employees";
        }
    }

    @PostMapping("/save")
    public String saveEmployee(@Valid @ModelAttribute("employee") Employee employee,
                              BindingResult bindingResult,
                              RedirectAttributes redirectAttributes,
                              HttpSession session,
                              Model model) {
        // Check admin access
        String accessCheck = checkAdminAccess(session);
        if (accessCheck != null) {
            return accessCheck;
        }

        // Handle validation errors
        if (bindingResult.hasErrors()) {
            model.addAttribute("employee", employee);
            model.addAttribute("pageTitle", employee.getId() != null ? "Edit Employee" : "Add New Employee");
            return "employee-form";
        }

        try {
            Employee savedEmployee = employeeService.saveEmployee(employee);
            if (employee.getId() == null) {
                redirectAttributes.addFlashAttribute("successMessage",
                    "Employee '" + savedEmployee.getFirstName() + "' has been added successfully!");
            } else {
                redirectAttributes.addFlashAttribute("successMessage",
                    "Employee '" + savedEmployee.getFirstName() + "' has been updated successfully!");
            }
            return "redirect:/employees";
        } catch (IllegalArgumentException e) {
            model.addAttribute("employee", employee);
            model.addAttribute("pageTitle", employee.getId() != null ? "Edit Employee" : "Add New Employee");
            model.addAttribute("errorMessage", e.getMessage());
            return "employee-form";
        }
    }

    @GetMapping("/delete/{id}")
    public String deleteEmployee(@PathVariable("id") Long id, RedirectAttributes redirectAttributes, HttpSession session) {
        // Check admin access
        String accessCheck = checkAdminAccess(session);
        if (accessCheck != null) {
            return accessCheck;
        }

        Optional<Employee> employee = employeeService.getEmployeeById(id);
        if (employee.isPresent()) {
            boolean deleted = employeeService.deleteEmployee(id);
            if (deleted) {
                redirectAttributes.addFlashAttribute("successMessage",
                    "Employee '" + employee.get().getFirstName() + "' has been deleted successfully!");
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "Failed to delete employee.");
            }
        } else {
            redirectAttributes.addFlashAttribute("errorMessage", "Employee not found with ID: " + id);
        }
        return "redirect:/employees";
    }

    @GetMapping("/")
    public String redirectToEmployees() {
        return "redirect:/employees";
    }
}
