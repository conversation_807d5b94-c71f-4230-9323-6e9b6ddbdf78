package com.example.lab07.controller;

import com.example.lab07.model.User;
import com.example.lab07.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.servlet.http.HttpSession;
import java.util.Optional;

@Controller
public class AuthController {

    @Autowired
    private UserService userService;

    @GetMapping("/login")
    public String login(@RequestParam(value = "error", required = false) String error,
                       @RequestParam(value = "logout", required = false) String logout,
                       Model model) {

        if (error != null) {
            model.addAttribute("errorMessage", "Invalid username or password!");
        }

        if (logout != null) {
            model.addAttribute("successMessage", "You have been logged out successfully!");
        }

        return "login";
    }

    @PostMapping("/login")
    public String processLogin(@RequestParam("username") String username,
                              @RequestParam("password") String password,
                              HttpSession session,
                              RedirectAttributes redirectAttributes) {

        // Authenticate user using our UserService
        boolean isAuthenticated = userService.login(username, password);

        if (isAuthenticated) {
            // Get user details and store in session
            Optional<User> userOpt = userService.findByUsername(username);
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                session.setAttribute("loggedInUser", user);
                session.setAttribute("username", user.getUsername());
                session.setAttribute("name", user.getName());
                session.setAttribute("role", user.getRole());

                // Redirect to employees page on successful login
                return "redirect:/employees";
            }
        }

        // Login failed - redirect back to login with error
        redirectAttributes.addFlashAttribute("errorMessage", "Invalid username or password!");
        return "redirect:/login?error=true";
    }

    @GetMapping("/logout")
    public String logout(HttpSession session, RedirectAttributes redirectAttributes) {
        // Clear session
        session.invalidate();

        redirectAttributes.addFlashAttribute("successMessage", "You have been logged out successfully!");
        return "redirect:/login?logout=true";
    }
}