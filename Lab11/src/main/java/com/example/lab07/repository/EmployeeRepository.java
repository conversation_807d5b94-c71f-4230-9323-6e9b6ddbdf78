package com.example.lab07.repository;

import com.example.lab07.model.Employee;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long> {
    Optional<Employee> findByEmailIgnoreCase(String email);

    @Query("SELECT e FROM Employee e WHERE LOWER(e.email) = LOWER(:email) AND e.id != :id")
    Optional<Employee> findByEmailIgnoreCaseAndIdNot(@Param("email") String email, @Param("id") Long id);
}
