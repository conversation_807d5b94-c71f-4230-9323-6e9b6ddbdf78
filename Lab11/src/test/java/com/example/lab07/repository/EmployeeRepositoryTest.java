package com.example.lab07.repository;

import com.example.lab07.model.Employee;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
@ActiveProfiles("test")
class EmployeeRepositoryTest {

    @Autowired
    private EmployeeRepository employeeRepository;

    private Employee employee1;
    private Employee employee2;

    @BeforeEach
    void setUp() {
        employeeRepository.deleteAll();

        employee1 = new Employee();
        employee1.setFirstName("John");
        employee1.setLastName("Doe");
        employee1.setEmail("<EMAIL>");

        employee2 = new Employee();
        employee2.setFirstName("Jane");
        employee2.setLastName("Smith");
        employee2.setEmail("<EMAIL>");

        employeeRepository.saveAll(List.of(employee1, employee2));
    }

    // ========== BASIC CRUD TESTS ==========

    @Test
    void testSaveAndFindById() {
        Employee newEmployee = new Employee();
        newEmployee.setFirstName("Test");
        newEmployee.setLastName("User");
        newEmployee.setEmail("<EMAIL>");

        Employee saved = employeeRepository.save(newEmployee);

        assertThat(saved.getId()).isNotNull();

        Optional<Employee> found = employeeRepository.findById(saved.getId());
        assertThat(found).isPresent();
        assertThat(found.get().getFirstName()).isEqualTo("Test");
        assertThat(found.get().getLastName()).isEqualTo("User");
        assertThat(found.get().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void testFindAll() {
        List<Employee> employees = employeeRepository.findAll();
        assertThat(employees).hasSize(2);
    }

    @Test
    void testDeleteById() {
        Employee newEmployee = new Employee();
        newEmployee.setFirstName("Delete");
        newEmployee.setLastName("Me");
        newEmployee.setEmail("<EMAIL>");

        Employee saved = employeeRepository.save(newEmployee);
        Long id = saved.getId();

        employeeRepository.deleteById(id);

        Optional<Employee> found = employeeRepository.findById(id);
        assertThat(found).isEmpty();
    }

    @Test
    void testExistsById() {
        Employee newEmployee = new Employee();
        newEmployee.setFirstName("Exists");
        newEmployee.setLastName("Test");
        newEmployee.setEmail("<EMAIL>");

        Employee saved = employeeRepository.save(newEmployee);

        boolean exists = employeeRepository.existsById(saved.getId());
        assertThat(exists).isTrue();

        boolean notExists = employeeRepository.existsById(999L);
        assertThat(notExists).isFalse();
    }

    @Test
    void testCount() {
        long count = employeeRepository.count();
        assertThat(count).isEqualTo(2);
    }

    // ========== EMAIL VERIFICATION TESTS ==========

    @Test
    void testFindByEmailIgnoreCase() {
        Optional<Employee> found = employeeRepository.findByEmailIgnoreCase("<EMAIL>");
        assertThat(found).isPresent();
        assertThat(found.get().getFirstName()).isEqualTo("John");
    }

    @Test
    void testFindByEmailIgnoreCaseAndIdNot() {
        Optional<Employee> found = employeeRepository.findByEmailIgnoreCaseAndIdNot("<EMAIL>", 999L);
        assertThat(found).isPresent();
        assertThat(found.get().getFirstName()).isEqualTo("John");

        // Should not find when excluding the actual ID
        Optional<Employee> notFound = employeeRepository.findByEmailIgnoreCaseAndIdNot("<EMAIL>", employee1.getId());
        assertThat(notFound).isEmpty();
    }
}
